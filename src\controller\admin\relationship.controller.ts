import {
  Controller,
  Post,
  Put,
  Del,
  Get,
  Body,
  Param,
  Query,
  Inject,
  Files,
} from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';
import { JwtMiddleware } from '../../middleware/jwt.middleware';
import { AuthMiddleware } from '../../middleware/auth.middleware';
import { RelationshipService } from '../../service/relationship.service';
import { ExcelService } from '../../service/excel.service';
import {
  CreateRelationshipDTO,
  UpdateRelationshipDTO,
  RelationshipQueryDTO,
  BatchCreateRelationshipDTO,
} from '../../dto/relationship.dto';
import { BatchUpdateStatusDTO } from '../../dto/dictionary.dto';
import { promises as fs } from 'fs';

/**
 * 关系管理控制器
 */
@Controller('/admin/relationship', {
  middleware: [JwtMiddleware, AuthMiddleware],
})
export class AdminRelationshipController {
  @Inject()
  relationshipService: RelationshipService;

  @Inject()
  excelService: ExcelService;

  /**
   * 创建关系
   */
  @Post('/')
  @Validate()
  async create(@Body() createDto: CreateRelationshipDTO) {
    console.log('🔗 创建关系:', createDto);

    try {
      const data = await this.relationshipService.createRelationship(createDto);
      console.log('🔗 关系创建成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系创建失败:', error);
      throw error;
    }
  }

  /**
   * 更新关系
   */
  @Put('/:id')
  @Validate()
  async update(
    @Param('id') id: number,
    @Body() updateDto: UpdateRelationshipDTO
  ) {
    console.log('🔗 更新关系:', { id, updateDto });

    try {
      const data = await this.relationshipService.updateRelationship(
        id,
        updateDto
      );
      console.log('🔗 关系更新成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系更新失败:', error);
      throw error;
    }
  }

  /**
   * 删除关系
   */
  @Del('/:id')
  async delete(@Param('id') id: number) {
    console.log('🔗 删除关系:', id);

    try {
      await this.relationshipService.deleteRelationship(id);
      console.log('🔗 关系删除成功:', id);
      return { message: '删除成功' };
    } catch (error) {
      console.error('🔗 关系删除失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系详情
   */
  @Get('/:id')
  async getById(@Param('id') id: number) {
    console.log('🔗 获取关系详情:', id);

    try {
      const data = await this.relationshipService.getRelationshipById(id);
      console.log('🔗 关系详情获取成功:', data.id);
      return data;
    } catch (error) {
      console.error('🔗 关系详情获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系列表（分页）
   */
  @Get('/')
  @Validate()
  async getList(@Query() queryDto: RelationshipQueryDTO) {
    console.log('🔗 关系列表查询开始:', queryDto);

    try {
      const data = await this.relationshipService.getRelationshipPage(queryDto);
      console.log('🔗 关系列表查询成功:', {
        total: data.total,
        page: data.page,
        pageSize: data.pageSize,
      });
      return data;
    } catch (error) {
      console.error('🔗 关系列表查询失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有关系（不分页）
   */
  @Get('/all')
  async getAll(@Query() filters?: any) {
    console.log('🔗 获取所有关系');

    try {
      const data = await this.relationshipService.getRelationshipList(filters);
      console.log('🔗 所有关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 所有关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 批量创建关系
   */
  @Post('/batch')
  @Validate()
  async batchCreate(@Body() batchDto: BatchCreateRelationshipDTO) {
    console.log('🔗 批量创建关系:', batchDto.relations.length);

    try {
      await this.relationshipService.batchCreateRelationships(
        batchDto.relations
      );
      console.log('🔗 批量创建关系成功');
      return { message: '批量创建成功' };
    } catch (error) {
      console.error('🔗 批量创建关系失败:', error);
      throw error;
    }
  }

  /**
   * 批量更新状态
   */
  @Put('/batch-status')
  @Validate()
  async batchUpdateStatus(@Body() batchDto: BatchUpdateStatusDTO) {
    console.log('🔗 批量更新关系状态:', batchDto);

    try {
      await this.relationshipService.batchUpdateStatus(
        batchDto.ids,
        batchDto.status
      );
      console.log('🔗 批量更新关系状态成功');
      return { message: '批量更新成功' };
    } catch (error) {
      console.error('🔗 批量更新关系状态失败:', error);
      throw error;
    }
  }

  /**
   * 根据要素获取关联关系
   */
  @Get('/by-element/:elementType/:elementId')
  async getByElement(
    @Param('elementType') elementType: string,
    @Param('elementId') elementId: number
  ) {
    console.log('🔗 根据要素获取关联关系:', { elementType, elementId });

    try {
      const data = await this.relationshipService.getRelationsByElement(
        elementType,
        elementId
      );
      console.log('🔗 要素关联关系获取成功:', data.length);
      return data;
    } catch (error) {
      console.error('🔗 要素关联关系获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取关系统计
   */
  @Get('/statistics/overview')
  async getStatistics(@Query() filters?: any) {
    console.log('🔗 获取关系统计');

    try {
      const data = await this.relationshipService.getRelationshipStatistics(
        filters
      );
      console.log('🔗 关系统计获取成功');
      return data;
    } catch (error) {
      console.error('🔗 关系统计获取失败:', error);
      throw error;
    }
  }

  /**
   * 获取网络图数据
   */
  @Get('/network-graph')
  async getNetworkGraph(@Query() filters?: any) {
    console.log('🔗 获取网络图数据');

    try {
      const data = await this.relationshipService.getNetworkGraphData(filters);
      console.log('🔗 网络图数据获取成功:', {
        nodes: data.nodes.length,
        links: data.links.length,
        categories: data.categories.length,
      });
      return data;
    } catch (error) {
      console.error('🔗 网络图数据获取失败:', error);
      throw error;
    }
  }

  // ==================== 导入相关接口 ====================

  /**
   * 获取导入模板
   */
  @Get('/template/download')
  async downloadTemplate() {
    console.log('🔗 获取要素关联导入模板');

    try {
      const buffer = await this.excelService.generateRelationshipTemplate();
      return {
        downloadUrl: '/public/templates/relationship_import_template.xlsx',
        filename: '要素关联导入模板.xlsx',
        description: '点击链接下载Excel导入模板，包含字段说明和示例数据',
        buffer: buffer.toString('base64'),
      };
    } catch (error) {
      console.error('🔗 获取导入模板失败:', error);
      throw new Error(`获取模板失败: ${error.message}`);
    }
  }

  /**
   * 预览导入数据
   */
  @Post('/import/preview', { middleware: [UploadMiddleware] })
  @Validate()
  async previewImport(@Files() files: UploadFileInfo[]) {
    console.log('🔗 预览要素关联导入数据');

    if (!files || files.length === 0) {
      throw new Error('请选择要上传的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseRelationshipExcel(
        file.data as string
      );

      return {
        success: parseResult.success,
        message: parseResult.success ? '文件格式正确' : '文件存在错误',
        errors: parseResult.errors,
        totalRows: parseResult.totalRows || 0,
        validRows: parseResult.validRows || 0,
        preview: parseResult.data?.slice(0, 5), // 只返回前5条数据作为预览
      };
    } catch (error) {
      return {
        success: false,
        message: `文件解析失败: ${error.message}`,
        totalRows: 0,
        validRows: 0,
      };
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 批量导入要素关联数据
   */
  @Post('/import/execute', { middleware: [UploadMiddleware] })
  @Validate()
  async executeImport(@Files() files: UploadFileInfo[]) {
    if (!files || files.length === 0) {
      throw new Error('请选择要导入的Excel文件');
    }

    const file = files[0];

    // 验证文件类型
    if (!this.isExcelFile(file.filename)) {
      throw new Error('请上传Excel文件（.xlsx或.xls格式）');
    }

    // 验证文件大小（限制为10MB）
    const fileSize = await this.getFileSize(file);
    if (fileSize > 10 * 1024 * 1024) {
      throw new Error('文件大小不能超过10MB');
    }

    try {
      // 解析Excel文件
      const parseResult = await this.excelService.parseRelationshipExcel(
        file.data as string
      );

      if (!parseResult.success || !parseResult.data) {
        throw new Error('文件解析失败或数据为空');
      }

      // 批量导入数据
      const importResult =
        await this.relationshipService.batchImportRelationships(
          parseResult.data
        );

      return {
        success: true,
        message: '导入完成',
        totalRows: parseResult.totalRows,
        validRows: parseResult.validRows,
        successCount: importResult.successCount,
        failureCount: importResult.failureCount,
        errors: importResult.errors,
      };
    } catch (error) {
      throw new Error(`导入失败: ${error.message}`);
    } finally {
      // 清理临时文件
      try {
        if (typeof file.data === 'string') {
          await fs.unlink(file.data);
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError);
      }
    }
  }

  /**
   * 批量导入接口（程序调用）
   */
  @Post('/batch-import')
  @Validate()
  async batchImport(@Body() data: { relationships: CreateRelationshipDTO[] }) {
    console.log('🔗 批量导入要素关联:', data.relationships.length);

    try {
      const result = await this.relationshipService.batchImportRelationships(
        data.relationships
      );
      console.log('🔗 批量导入要素关联成功:', result);
      return {
        message: '批量导入完成',
        ...result,
      };
    } catch (error) {
      console.error('🔗 批量导入要素关联失败:', error);
      throw error;
    }
  }

  /**
   * 验证是否为Excel文件
   */
  private isExcelFile(filename: string): boolean {
    const extension = filename.toLowerCase().split('.').pop();
    return extension === 'xlsx' || extension === 'xls';
  }

  /**
   * 获取文件大小
   */
  private async getFileSize(file: UploadFileInfo): Promise<number> {
    if (typeof file.data === 'string') {
      const stats = await fs.stat(file.data);
      return stats.size;
    }
    return 0;
  }
}
