import { Inject, Middleware } from '@midwayjs/core';
import { Context, NextFunction } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import { UserService } from '../service/user.service';

@Middleware()
export class JwtMiddleware {
  @Inject()
  jwtService: JwtService;

  @Inject()
  userService: UserService;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 获取token
      const token = this.extractTokenFromHeader(ctx);
      if (!token) {
        ctx.status = 401;
        ctx.body = {
          success: false,
          message: '未提供认证令牌',
          code: 401,
        };
        return;
      }

      try {
        // 验证token
        const payload = (await this.jwtService.verify(token)) as any;

        // 验证用户是否存在且启用
        const user = await this.userService.validateUser(payload.userId);

        // 将用户信息添加到上下文
        ctx.state.user = {
          id: user.id,
          username: user.username,
          role: user.role,
        };

        await next();
      } catch (error) {
        // 只处理JWT相关的错误，其他错误直接抛出交给过滤器处理
        if (this.isJwtError(error)) {
          ctx.throw(401, '认证令牌无效或已过期');
        } else {
          throw error; // 直接抛出，不修改错误类型
        }
      }
    };
  }

  private extractTokenFromHeader(ctx: Context): string | null {
    const authorization = ctx.headers.authorization;
    if (!authorization) {
      return null;
    }

    const parts = authorization.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * 判断是否是JWT相关的错误
   */
  private isJwtError(error: any): boolean {
    // 只检查明确的JWT错误类型
    const jwtErrorTypes = [
      'JsonWebTokenError',
      'TokenExpiredError',
      'NotBeforeError',
    ];

    return jwtErrorTypes.includes(error.name);
  }

  ignore(ctx: Context): boolean {
    // 跳过不需要认证的路径
    const skipPaths = ['/openapi', '/admin/auth/login', '/health'];

    // 下面的路由将忽略此中间件
    return skipPaths.some(path => ctx.path.startsWith(path));
  }
  static getName(): string {
    return 'API_JWT';
  }
}
