# 前端门户详情页接口汇总

## 概述

本文档汇总了前端门户详情页所需的所有接口，包括山塬、水系和历史要素的详情展示相关接口。详情页主要分为三个区域：

- **左侧区域**：基本信息展示（名称、坐标、描述等）
- **中间区域**：相关图片展示
- **右侧区域**：关联信息展示（与其他要素的关系）

所有接口均为公开接口，无需认证即可访问。

---

## 统一响应格式

### 成功响应
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    // 具体业务数据
  }
}
```

### 错误响应
```json
{
  "errCode": 400,  // 错误码
  "msg": "错误信息"  // 错误描述
}
```

---

## 1. 山塬详情页接口

### 1.1 获取山塬详情
获取山塬的基本信息，包括名称、坐标、高度、历史记载等。

**接口地址：** `GET /openapi/mountain/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "华山",
    "code": "HS001",
    "longitude": 110.0910,
    "latitude": 34.4880,
    "height": 2154,
    "historicalRecords": "华山，古称"西岳"，雅称"太华山"，为中国著名的五岳之一。",
    "regionDictId": 1,
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    }
  }
}
```

### 1.2 获取山塬照片
获取指定山塬的照片列表。

**接口地址：** `GET /openapi/mountain/{id}/photos`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "华山主峰",
      "url": "/public/uploads/2024/01/15/huashan_main_peak.jpg",
      "description": "华山主峰景色"
    },
    {
      "id": 2,
      "name": "华山日出",
      "url": "/public/uploads/2024/01/15/huashan_sunrise.jpg",
      "description": "华山日出美景"
    }
  ]
}
```

### 1.3 获取山塬关联信息
获取与指定山塬相关联的其他要素信息，用于右侧关联信息展示。

**接口地址：** `GET /openapi/relationship/by-element/mountain/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 山塬ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "sourceType": "mountain",
      "sourceElement": {
        "id": 1,
        "name": "华山",
        "code": "HS001"
      },
      "targetType": "waterSystem",
      "targetElement": {
        "id": 1,
        "name": "渭河",
        "code": "WH001"
      },
      "relationDict": {
        "id": 1,
        "relationName": "临近",
        "relationCode": "ADJACENT"
      },
      "direction": "bidirectional",
      "term": "地理位置",
      "record": "华山与渭河地理位置相邻"
    },
    {
      "id": 2,
      "sourceType": "mountain",
      "sourceElement": {
        "id": 1,
        "name": "华山",
        "code": "HS001"
      },
      "targetType": "historicalElement",
      "targetElement": {
        "id": 2,
        "name": "华清池",
        "code": "HQC001"
      },
      "relationDict": {
        "id": 2,
        "relationName": "历史关联",
        "relationCode": "HISTORICAL"
      },
      "direction": "unidirectional",
      "term": "历史文化",
      "record": "华山与华清池在历史上有文化关联"
    }
  ]
}
```

---

## 2. 水系详情页接口

### 2.1 获取水系详情
获取水系的基本信息，包括名称、坐标、长度面积、历史记载等。

**接口地址：** `GET /openapi/water-system/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "渭河",
    "code": "WH001",
    "longitude": 108.9633,
    "latitude": 34.2658,
    "lengthArea": "818公里",
    "historicalRecords": "渭河是黄河的最大支流，流经关中平原，是关中地区的母亲河。",
    "regionDictId": 1,
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    }
  }
}
```

### 2.2 获取水系照片
获取指定水系的照片列表。

**接口地址：** `GET /openapi/water-system/{id}/photos`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "渭河全景",
      "url": "/public/uploads/2024/01/15/weihe_panorama.jpg",
      "description": "渭河全景图"
    },
    {
      "id": 2,
      "name": "渭河夕阳",
      "url": "/public/uploads/2024/01/15/weihe_sunset.jpg",
      "description": "渭河夕阳西下"
    }
  ]
}
```

### 2.3 获取水系关联信息
获取与指定水系相关联的其他要素信息，用于右侧关联信息展示。

**接口地址：** `GET /openapi/relationship/by-element/waterSystem/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 水系ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 3,
      "sourceType": "waterSystem",
      "sourceElement": {
        "id": 1,
        "name": "渭河",
        "code": "WH001"
      },
      "targetType": "mountain",
      "targetElement": {
        "id": 1,
        "name": "华山",
        "code": "HS001"
      },
      "relationDict": {
        "id": 1,
        "relationName": "临近",
        "relationCode": "ADJACENT"
      },
      "direction": "bidirectional",
      "term": "地理位置",
      "record": "渭河流经华山附近区域"
    },
    {
      "id": 4,
      "sourceType": "waterSystem",
      "sourceElement": {
        "id": 1,
        "name": "渭河",
        "code": "WH001"
      },
      "targetType": "historicalElement",
      "targetElement": {
        "id": 3,
        "name": "大明宫",
        "code": "DMG001"
      },
      "relationDict": {
        "id": 3,
        "relationName": "供水关系",
        "relationCode": "WATER_SUPPLY"
      },
      "direction": "unidirectional",
      "term": "功能关系",
      "record": "渭河为大明宫提供水源"
    }
  ]
}
```

---

## 3. 历史要素详情页接口

### 3.1 获取历史要素详情
获取历史要素的基本信息，包括名称、坐标、建造时间、历史记载等。

**接口地址：** `GET /openapi/historical-element/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "id": 1,
    "name": "大雁塔",
    "code": "DYT001",
    "typeDictId": 1,
    "constructionLongitude": 108.9640,
    "constructionLatitude": 34.2180,
    "locationDescription": "位于西安市雁塔区大慈恩寺内",
    "constructionTime": "652-01-01T00:00:00.000Z",
    "historicalRecords": "大雁塔又名"慈恩寺塔"，唐永徽三年（652年），玄奘为保存由天竺经丝绸之路带回长安的经卷佛像主持修建了大雁塔。",
    "regionDictId": 1,
    "typeDict": {
      "id": 1,
      "typeName": "佛塔",
      "typeCode": "PAGODA"
    },
    "regionDict": {
      "id": 1,
      "regionName": "关中地区",
      "regionCode": "GUANZHONG"
    }
  }
}
```

### 3.2 获取历史要素照片
获取指定历史要素的照片列表。

**接口地址：** `GET /openapi/historical-element/{id}/photos`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 1,
      "name": "大雁塔全景",
      "url": "/public/uploads/2024/01/15/dayanta_panorama.jpg",
      "description": "大雁塔全景图"
    },
    {
      "id": 2,
      "name": "大雁塔夜景",
      "url": "/public/uploads/2024/01/15/dayanta_night.jpg",
      "description": "大雁塔夜景"
    }
  ]
}
```

### 3.3 获取历史要素关联信息
获取与指定历史要素相关联的其他要素信息，用于右侧关联信息展示。

**接口地址：** `GET /openapi/relationship/by-element/historicalElement/{id}`

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | number | 是 | 历史要素ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": [
    {
      "id": 5,
      "sourceType": "historicalElement",
      "sourceElement": {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001"
      },
      "targetType": "historicalElement",
      "targetElement": {
        "id": 2,
        "name": "小雁塔",
        "code": "XYT001"
      },
      "relationDict": {
        "id": 4,
        "relationName": "同类建筑",
        "relationCode": "SAME_TYPE"
      },
      "direction": "bidirectional",
      "term": "建筑类型",
      "record": "大雁塔与小雁塔都是唐代佛塔建筑"
    },
    {
      "id": 6,
      "sourceType": "historicalElement",
      "sourceElement": {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001"
      },
      "targetType": "mountain",
      "targetElement": {
        "id": 2,
        "name": "终南山",
        "code": "ZNS001"
      },
      "relationDict": {
        "id": 5,
        "relationName": "遥望关系",
        "relationCode": "VIEW_RELATION"
      },
      "direction": "unidirectional",
      "term": "视觉关系",
      "record": "从大雁塔可以遥望终南山"
    }
  ]
}
```

---

## 4. 列表查询接口

### 4.1 获取山塬列表
分页获取山塬数据列表，支持关键词搜索和区域筛选。

**接口地址：** `GET /openapi/mountain/list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10，最大100 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "height": 2154,
        "regionDictId": 1,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 4.2 获取水系列表
分页获取水系数据列表，支持关键词搜索和区域筛选。

**接口地址：** `GET /openapi/water-system/list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10，最大100 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "渭河",
        "code": "WH001",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "lengthArea": "818公里",
        "regionDictId": 1,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 4.3 获取历史要素列表
分页获取历史要素数据列表，支持关键词搜索、区域筛选和类型筛选。

**接口地址：** `GET /openapi/historical-element/list`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10，最大100 |
| keyword | string | 否 | 搜索关键词，按名称模糊搜索 |
| regionId | number | 否 | 区域ID筛选 |
| typeId | number | 否 | 类型ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "大雁塔",
        "code": "DYT001",
        "typeDictId": 1,
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "constructionTime": "652-01-01T00:00:00.000Z",
        "regionDictId": 1,
        "typeDict": {
          "id": 1,
          "typeName": "佛塔",
          "typeCode": "PAGODA"
        },
        "regionDict": {
          "id": 1,
          "regionName": "关中地区",
          "regionCode": "GUANZHONG"
        }
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 4.4 统一搜索接口
跨类型搜索，在所有实体类型中进行统一检索。

**接口地址：** `GET /openapi/search`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词，至少2个字符 |
| type | string | 否 | 搜索类型：mountain/waterSystem/historicalElement/all，默认all |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10，最大100 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "type": "mountain",
        "id": 1,
        "name": "华山",
        "code": "HS001",
        "regionDictId": 1,
        "longitude": 110.0910,
        "latitude": 34.4880
      },
      {
        "type": "historicalElement",
        "id": 1,
        "name": "华清池",
        "code": "HQC001",
        "regionDictId": 1,
        "typeDictId": 2
      }
    ],
    "total": 2,
    "page": 1,
    "pageSize": 10,
    "summary": {
      "mountain": 1,
      "waterSystem": 0,
      "historicalElement": 1
    }
  }
}
```

---

## 5. 通用辅助接口

### 4.1 获取地图数据
获取用于地图展示的数据，支持按类型和区域筛选。

**接口地址：** `GET /openapi/map/data`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 数据类型：mountain/water_system/historical_element |
| regionId | number | 否 | 区域ID筛选 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "mountains": [
      {
        "id": 1,
        "name": "华山",
        "longitude": 110.0910,
        "latitude": 34.4880,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区"
        }
      }
    ],
    "waterSystems": [
      {
        "id": 1,
        "name": "渭河",
        "longitude": 108.9633,
        "latitude": 34.2658,
        "regionDict": {
          "id": 1,
          "regionName": "关中地区"
        }
      }
    ],
    "historicalElements": [
      {
        "id": 1,
        "name": "大雁塔",
        "constructionLongitude": 108.9640,
        "constructionLatitude": 34.2180,
        "typeDict": {
          "id": 1,
          "typeName": "佛塔"
        },
        "regionDict": {
          "id": 1,
          "regionName": "关中地区"
        }
      }
    ]
  }
}
```

### 5.2 获取字典数据
获取区域和类型字典数据，用于下拉选择等。

**接口地址：** `GET /openapi/dictionary/data`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | string | 否 | 字典类型：region/historical_element_type |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "regions": [
      {
        "id": 1,
        "regionName": "关中地区",
        "regionCode": "GUANZHONG"
      }
    ],
    "historicalElementTypes": [
      {
        "id": 1,
        "typeName": "佛塔",
        "typeCode": "PAGODA"
      }
    ]
  }
}
```

### 5.3 获取统计数据
获取基础统计数据，用于详情页展示相关统计信息。

**接口地址：** `GET /openapi/statistic/basic`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| regionId | number | 否 | 区域ID |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "counts": {
      "mountain": 25,
      "waterSystem": 15,
      "historicalElement": 50
    },
    "regionStats": [
      {
        "region": "关中地区",
        "regionId": 1,
        "mountainCount": 15,
        "waterSystemCount": 10,
        "historicalElementCount": 35,
        "total": 60
      }
    ]
  }
}
```

### 5.4 获取关系网络图数据
获取关系网络的图形化数据，用于构建可视化的关系网络图。

**接口地址：** `GET /openapi/relationship/network-graph`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| sourceType | string | 否 | 源实体类型：mountain/waterSystem/historicalElement |
| targetType | string | 否 | 目标实体类型：mountain/waterSystem/historicalElement |
| relationDictId | number | 否 | 关系类型ID |
| status | number | 否 | 状态筛选（1=启用，0=禁用），默认1 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "nodes": [
      {
        "id": "mountain_1",
        "name": "华山",
        "type": "mountain",
        "category": "山塬",
        "size": 20,
        "color": "#ff6b6b"
      },
      {
        "id": "waterSystem_1",
        "name": "渭河",
        "type": "waterSystem",
        "category": "水系",
        "size": 15,
        "color": "#4ecdc4"
      }
    ],
    "links": [
      {
        "source": "mountain_1",
        "target": "waterSystem_1",
        "relation": "临近",
        "direction": "bidirectional",
        "weight": 1
      }
    ],
    "categories": [
      {
        "name": "山塬",
        "color": "#ff6b6b"
      },
      {
        "name": "水系",
        "color": "#4ecdc4"
      }
    ]
  }
}
```

### 5.5 获取关系统计数据
获取关系数据的统计信息，用于分析和展示。

**接口地址：** `GET /openapi/relationship/statistics`

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "totalRelations": 156,
    "byRelationType": [
      {
        "relationId": 1,
        "relationName": "临近",
        "relationCode": "ADJACENT",
        "count": 45
      },
      {
        "relationId": 2,
        "relationName": "历史关联",
        "relationCode": "HISTORICAL",
        "count": 32
      }
    ],
    "bySourceType": [
      {
        "type": "mountain",
        "count": 68
      },
      {
        "type": "waterSystem",
        "count": 42
      },
      {
        "type": "historicalElement",
        "count": 46
      }
    ],
    "byTargetType": [
      {
        "type": "mountain",
        "count": 52
      },
      {
        "type": "waterSystem",
        "count": 38
      },
      {
        "type": "historicalElement",
        "count": 66
      }
    ]
  }
}
```

### 5.6 搜索关系数据
根据关键词搜索关系数据。

**接口地址：** `GET /openapi/relationship/search`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认10 |

**响应示例：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "list": [
      {
        "id": 1,
        "sourceElement": {
          "id": 1,
          "name": "华山",
          "type": "mountain"
        },
        "targetElement": {
          "id": 2,
          "name": "华清池",
          "type": "historicalElement"
        },
        "relationDict": {
          "id": 2,
          "relationName": "历史关联"
        },
        "record": "华山与华清池在历史上有文化关联"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
}
```

---

## 6. 数据字段说明

### 6.1 山塬数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 山塬唯一标识 |
| name | string | 山塬名称 |
| code | string | 山塬编号 |
| longitude | number | 经度坐标（WGS84） |
| latitude | number | 纬度坐标（WGS84） |
| height | number | 高度（米） |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域详细信息 |

### 6.2 水系数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 水系唯一标识 |
| name | string | 水系名称 |
| code | string | 水系编号 |
| longitude | number | 经度坐标（WGS84） |
| latitude | number | 纬度坐标（WGS84） |
| lengthArea | string | 长度/面积描述 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| regionDict | object | 区域详细信息 |

### 6.3 历史要素数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 历史要素唯一标识 |
| name | string | 历史要素名称 |
| code | string | 历史要素编号 |
| typeDictId | number | 所属类型ID |
| constructionLongitude | number | 建筑经度坐标（WGS84） |
| constructionLatitude | number | 建筑纬度坐标（WGS84） |
| locationDescription | string | 位置描述 |
| constructionTime | string | 建造时间 |
| historicalRecords | string | 历史文献记载 |
| regionDictId | number | 所属区域ID |
| typeDict | object | 类型详细信息 |
| regionDict | object | 区域详细信息 |

### 6.4 照片数据字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | number | 照片唯一标识 |
| name | string | 照片名称 |
| url | string | 照片访问URL |
| description | string | 照片描述 |

---

## 7. 使用说明

### 7.1 接口基础路径
所有接口的基础路径为：`/openapi`

### 7.2 坐标系统
所有坐标数据使用WGS84坐标系统。

### 7.3 时间格式
建造时间使用ISO 8601格式。

### 7.4 图片访问
照片URL为相对路径，需要拼接服务器域名访问。

### 7.5 错误处理
所有接口都遵循统一的错误响应格式，前端需要根据errCode判断请求是否成功。

---

## 8. 前端集成示例

### 8.1 获取详情页数据
```javascript
// 获取山塬详情
async function getMountainDetail(id) {
  const response = await fetch(`/openapi/mountain/${id}`);
  const result = await response.json();
  if (result.errCode === 0) {
    return result.data;
  } else {
    throw new Error(result.msg);
  }
}

// 获取照片列表
async function getMountainPhotos(id) {
  const response = await fetch(`/openapi/mountain/${id}/photos`);
  const result = await response.json();
  if (result.errCode === 0) {
    return result.data;
  } else {
    throw new Error(result.msg);
  }
}
```

### 8.2 完整详情页数据获取
```javascript
// 获取完整的详情页数据（包含关联信息）
async function getDetailPageData(type, id) {
  try {
    // 转换类型名称以匹配关联接口
    const elementTypeMap = {
      'mountain': 'mountain',
      'water-system': 'waterSystem',
      'historical-element': 'historicalElement'
    };
    const elementType = elementTypeMap[type] || type;

    // 并行获取基本信息、照片和关联信息
    const [detail, photos, relations] = await Promise.all([
      fetch(`/openapi/${type}/${id}`).then(res => res.json()),
      fetch(`/openapi/${type}/${id}/photos`).then(res => res.json()),
      fetch(`/openapi/relationship/by-element/${elementType}/${id}`).then(res => res.json())
    ]);

    if (detail.errCode === 0 && photos.errCode === 0 && relations.errCode === 0) {
      return {
        detail: detail.data,
        photos: photos.data,
        relations: relations.data
      };
    } else {
      throw new Error(detail.msg || photos.msg || relations.msg);
    }
  } catch (error) {
    console.error('获取详情页数据失败:', error);
    throw error;
  }
}

// 使用示例
getDetailPageData('mountain', 1).then(data => {
  console.log('山塬详情:', data.detail);
  console.log('山塬照片:', data.photos);
  console.log('关联信息:', data.relations);

  // 渲染关联信息
  renderRelations(data.relations);
});

// 渲染关联信息的示例函数
function renderRelations(relations) {
  const relationsContainer = document.getElementById('relations-container');

  if (relations.length === 0) {
    relationsContainer.innerHTML = '<p>暂无关联信息</p>';
    return;
  }

  const relationsHtml = relations.map(relation => {
    const target = relation.targetElement;
    const relationType = relation.relationDict.relationName;
    const term = relation.term || '';
    const record = relation.record || '';

    return `
      <div class="relation-item">
        <h4><a href="/detail/${relation.targetType}/${target.id}">${target.name}</a></h4>
        <p class="relation-type">关系类型: ${relationType}</p>
        ${term ? `<p class="relation-term">关系领域: ${term}</p>` : ''}
        ${record ? `<p class="relation-record">${record}</p>` : ''}
      </div>
    `;
  }).join('');

  relationsContainer.innerHTML = `
    <h3>关联信息 (${relations.length})</h3>
    <div class="relations-list">${relationsHtml}</div>
  `;
}
```

---

## 9. 接口调用顺序建议

### 9.1 详情页初始化流程
```javascript
// 1. 获取基本详情信息
const detail = await fetch(`/openapi/${type}/${id}`).then(res => res.json());

// 2. 转换类型名称以匹配关联接口
const elementTypeMap = {
  'mountain': 'mountain',
  'water-system': 'waterSystem',
  'historical-element': 'historicalElement'
};
const elementType = elementTypeMap[type] || type;

// 3. 并行获取照片和关联信息
const [photos, relations] = await Promise.all([
  fetch(`/openapi/${type}/${id}/photos`).then(res => res.json()),
  fetch(`/openapi/relationship/by-element/${elementType}/${id}`).then(res => res.json())
]);

// 4. 可选：获取统计数据（如果需要显示区域统计）
const stats = await fetch(`/openapi/statistic/basic?regionId=${detail.data.regionDictId}`).then(res => res.json());

// 5. 可选：获取关系网络图数据（如果需要显示关系图）
const networkGraph = await fetch('/openapi/relationship/network-graph').then(res => res.json());
```

### 9.2 地图集成流程
```javascript
// 1. 获取地图数据
const mapData = await fetch('/openapi/map/data');

// 2. 点击地图标记时获取详情
const onMarkerClick = async (type, id) => {
  const detail = await fetch(`/openapi/${type}/${id}`);
  // 显示详情弹窗或跳转详情页
};
```

---

## 10. 性能优化建议

### 10.1 数据缓存
- 字典数据（区域、类型）可以缓存在本地存储
- 地图数据可以按区域分块缓存
- 详情数据可以设置短期缓存（5-10分钟）

### 10.2 图片优化
- 照片列表支持懒加载
- 可以先加载缩略图，点击后加载原图
- 建议使用图片CDN加速访问

### 10.3 接口合并
- 详情页可以考虑合并基本信息和照片接口
- 地图数据可以按需加载，避免一次性加载所有数据

---

## 11. 常见问题

### 11.1 坐标转换
- 所有坐标使用WGS84标准
- 如需其他坐标系，前端需要进行坐标转换

### 11.2 图片访问
- 照片URL为相对路径，需要拼接完整域名
- 建议配置图片服务器域名常量

### 11.3 错误处理
- 所有接口都可能返回错误，需要统一错误处理
- 建议封装统一的API调用方法

### 11.4 数据为空处理
- 照片列表可能为空，需要显示默认图片
- 历史记载可能为空，需要显示默认文案

---

## 12. 接口测试

### 12.1 测试用例
```bash
# 测试山塬详情
curl -X GET "http://localhost:7001/openapi/mountain/1"

# 测试山塬照片
curl -X GET "http://localhost:7001/openapi/mountain/1/photos"

# 测试山塬关联信息
curl -X GET "http://localhost:7001/openapi/relationship/by-element/mountain/1"

# 测试水系详情
curl -X GET "http://localhost:7001/openapi/water-system/1"

# 测试水系照片
curl -X GET "http://localhost:7001/openapi/water-system/1/photos"

# 测试水系关联信息
curl -X GET "http://localhost:7001/openapi/relationship/by-element/waterSystem/1"

# 测试历史要素详情
curl -X GET "http://localhost:7001/openapi/historical-element/1"

# 测试历史要素照片
curl -X GET "http://localhost:7001/openapi/historical-element/1/photos"

# 测试历史要素关联信息
curl -X GET "http://localhost:7001/openapi/relationship/by-element/historicalElement/1"

# 测试关系网络图数据
curl -X GET "http://localhost:7001/openapi/relationship/network-graph"

# 测试关系统计数据
curl -X GET "http://localhost:7001/openapi/relationship/statistics"

# 测试关系搜索
curl -X GET "http://localhost:7001/openapi/relationship/search?keyword=华山"

# 测试地图数据
curl -X GET "http://localhost:7001/openapi/map/data?type=mountain&regionId=1"

# 测试统一搜索
curl -X GET "http://localhost:7001/openapi/search?keyword=华&type=all"
```

### 12.2 预期响应
所有接口都应该返回errCode为0的成功响应，如果返回其他错误码，需要检查：
- 参数是否正确
- 数据是否存在
- 服务是否正常运行

---

## 13. 联系方式

如有接口问题或需要技术支持，请联系开发团队。

**文档版本：** v1.0
**最后更新：** 2024-01-15
