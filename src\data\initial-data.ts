/**
 * 系统初始化数据配置
 */

/**
 * 区域字典初始数据
 */
export const INITIAL_REGION_DATA = [
  {
    regionCode: 'REGION_XIAN',
    regionName: '西安',
    parentId: null,
    status: 1,
    sort: 1,
    regionDesc: '西安市及其周边区域',
  },
  {
    regionCode: 'REGION_BAISHUI',
    regionName: '白水',
    parentId: null,
    status: 1,
    sort: 2,
    regionDesc: '白水县及其周边区域',
  },
];

/**
 * 类型字典初始数据
 * 包含三个基础类别：山塬、水系、历史要素
 */
export const INITIAL_TYPE_DATA = [
  // 基础类别
  {
    typeCode: 'TYPE_MOUNTAIN',
    typeName: '山塬',
    parentId: null,
    status: 1,
    sort: 1,
    typeDesc: '山峰、山塬等地形要素类型',
  },
  {
    typeCode: 'TYPE_WATER_SYSTEM',
    typeName: '水系',
    parentId: null,
    status: 1,
    sort: 2,
    typeDesc: '河流、湖泊等水系要素类型',
  },
  {
    typeCode: 'TYPE_HISTORICAL_ELEMENT',
    typeName: '历史要素',
    parentId: null,
    status: 1,
    sort: 3,
    typeDesc: '历史建筑、遗址等人文要素类型',
  },

  // 历史要素子类型
  {
    typeCode: 'TYPE_SHRINE_TEMPLE',
    typeName: '祠庙坛壝',
    parentId: null, // 将在初始化时设置为历史要素的ID
    status: 1,
    sort: 11,
    typeDesc: '祠堂、庙宇、祭坛、社稷等祭祀建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_PAVILION_TOWER',
    typeName: '亭台楼阁',
    parentId: null,
    status: 1,
    sort: 12,
    typeDesc: '亭子、台榭、楼房、阁楼等观赏建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_BUDDHIST_TAOIST',
    typeName: '佛寺道观',
    parentId: null,
    status: 1,
    sort: 13,
    typeDesc: '佛教寺院、道教宫观等宗教建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_TOMB_MOUND',
    typeName: '陵墓古冢',
    parentId: null,
    status: 1,
    sort: 14,
    typeDesc: '帝王陵墓、古代墓葬、封土堆等',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_ANCIENT_CITY',
    typeName: '故城遗址',
    parentId: null,
    status: 1,
    sort: 15,
    typeDesc: '古代城池遗址、废弃城镇遗迹',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_TEMPLE',
    typeName: '寺庙',
    parentId: null,
    status: 1,
    sort: 16,
    typeDesc: '佛教、道教等宗教建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_PALACE',
    typeName: '宫殿',
    parentId: null,
    status: 1,
    sort: 17,
    typeDesc: '皇家宫殿建筑群',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_TOMB',
    typeName: '陵墓',
    parentId: null,
    status: 1,
    sort: 18,
    typeDesc: '帝王陵墓、贵族墓葬',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_TOWER',
    typeName: '塔楼',
    parentId: null,
    status: 1,
    sort: 19,
    typeDesc: '佛塔、古塔等高层建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_CITY_WALL',
    typeName: '城墙',
    parentId: null,
    status: 1,
    sort: 20,
    typeDesc: '古代城市防御工事',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },
  {
    typeCode: 'TYPE_BRIDGE',
    typeName: '古桥',
    parentId: null,
    status: 1,
    sort: 21,
    typeDesc: '历史悠久的桥梁建筑',
    parentTypeCode: 'TYPE_HISTORICAL_ELEMENT',
  },

  // 山塬子类型
  {
    typeCode: 'TYPE_MAIN_PEAK',
    typeName: '主峰',
    parentId: null,
    status: 1,
    sort: 31,
    typeDesc: '山脉的主要峰峦',
    parentTypeCode: 'TYPE_MOUNTAIN',
  },
  {
    typeCode: 'TYPE_SECONDARY_PEAK',
    typeName: '次峰',
    parentId: null,
    status: 1,
    sort: 32,
    typeDesc: '山脉的次要峰峦',
    parentTypeCode: 'TYPE_MOUNTAIN',
  },
  {
    typeCode: 'TYPE_PLATEAU',
    typeName: '塬地',
    parentId: null,
    status: 1,
    sort: 33,
    typeDesc: '平坦的高地',
    parentTypeCode: 'TYPE_MOUNTAIN',
  },

  // 水系子类型
  {
    typeCode: 'TYPE_MAIN_RIVER',
    typeName: '主河',
    parentId: null,
    status: 1,
    sort: 51,
    typeDesc: '主要河流',
    parentTypeCode: 'TYPE_WATER_SYSTEM',
  },
  {
    typeCode: 'TYPE_TRIBUTARY',
    typeName: '支流',
    parentId: null,
    status: 1,
    sort: 52,
    typeDesc: '河流的支流',
    parentTypeCode: 'TYPE_WATER_SYSTEM',
  },
  {
    typeCode: 'TYPE_LAKE',
    typeName: '湖泊',
    parentId: null,
    status: 1,
    sort: 53,
    typeDesc: '天然或人工湖泊',
    parentTypeCode: 'TYPE_WATER_SYSTEM',
  },
];

/**
 * 关系字典初始数据
 */
export const INITIAL_RELATIONSHIP_DATA = [
  {
    relationCode: 'RELATION_LOCATION',
    relationName: '选址关联',
    parentId: null,
    status: 1,
    sort: 1,
    relationDesc: '山塬与城镇、建筑的选址依赖关系',
  },
  {
    relationCode: 'RELATION_VISUAL',
    relationName: '视线关联',
    parentId: null,
    status: 1,
    sort: 2,
    relationDesc: '不同要素间的视觉联系关系',
  },
  {
    relationCode: 'RELATION_EMOTION',
    relationName: '情感关联',
    parentId: null,
    status: 1,
    sort: 3,
    relationDesc: '人文要素与地理环境的情感纽带关系',
  },
  {
    relationCode: 'RELATION_ECOLOGY',
    relationName: '生态关联',
    parentId: null,
    status: 1,
    sort: 4,
    relationDesc: '自然要素间的生态依存关系',
  },
  {
    relationCode: 'RELATION_LANDSCAPE',
    relationName: '景观关联',
    parentId: null,
    status: 1,
    sort: 5,
    relationDesc: '要素间的景观构成与美学关系',
  },
  {
    relationCode: 'RELATION_SPATIAL',
    relationName: '空间关联',
    parentId: null,
    status: 1,
    sort: 6,
    relationDesc: '要素间的空间位置关系',
  },
  {
    relationCode: 'RELATION_HISTORICAL',
    relationName: '历史关联',
    parentId: null,
    status: 1,
    sort: 7,
    relationDesc: '要素间的历史文化关联',
  },
];

/**
 * 默认用户数据
 */
export const INITIAL_USER_DATA = {
  username: 'admin',
  password: 'admin123',
  role: 'admin',
  nickname: '系统管理员',
  isActive: true,
};

/**
 * 数据初始化配置
 */
export const INIT_CONFIG = {
  // 是否在初始化时显示详细日志
  verbose: true,
  // 是否强制重新初始化（清空现有数据）
  forceReinit: false,
  // 批量插入的批次大小
  batchSize: 100,
};
