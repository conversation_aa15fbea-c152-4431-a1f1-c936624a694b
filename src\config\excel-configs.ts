/**
 * Excel配置文件
 * 支持导入、导出等多种场景的灵活配置
 */

/**
 * Excel模板字段配置接口
 */
export interface ExcelTemplateFieldConfig {
  /** 字段标签 */
  label: string;
  /** 字段键名 */
  key: string;
  /** 是否必填 */
  required: boolean;
  /** 字段描述 */
  description?: string;
  /** 列宽 */
  width?: number;
  /** 数据类型 */
  type?: 'string' | 'number' | 'date' | 'boolean';
  /** 验证规则 */
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
}

/**
 * Excel模板配置接口
 */
export interface ExcelTemplateConfig {
  /** 模板标题 */
  title: string;
  /** 填写说明 */
  instructions: string[];
  /** 表头配置 */
  headers: ExcelTemplateFieldConfig[];
  /** 示例数据 */
  exampleData?: any[];
  /** 工作表名称 */
  sheetName?: string;
  /** 模板样式配置 */
  styleConfig?: {
    /** 是否启用样式 */
    enabled: boolean;
    /** 主题色 */
    theme?: 'blue' | 'green' | 'orange' | 'red';
    /** 是否显示边框 */
    showBorders?: boolean;
    /** 是否高亮必填字段 */
    highlightRequired?: boolean;
  };
}

/**
 * 历史要素字段定义（通用）
 */
const HISTORICAL_ELEMENT_FIELDS = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '历史要素名称，必填，最大255字符',
    width: 20,
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '历史要素编号，必填，最大50字符，系统内唯一',
    width: 15,
  },
  {
    label: '类型名称',
    key: 'typeName',
    required: false,
    description: '所属类型名称，可选，需要在类型字典中存在',
    width: 15,
  },
  {
    label: '建筑经度',
    key: 'constructionLongitude',
    required: false,
    description: '建筑经度坐标，可选，范围-180到180',
    width: 15,
  },
  {
    label: '建筑纬度',
    key: 'constructionLatitude',
    required: false,
    description: '建筑纬度坐标，可选，范围-90到90',
    width: 15,
  },
  {
    label: '位置描述',
    key: 'locationDescription',
    required: false,
    description: '位置描述信息，可选',
    width: 30,
  },
  {
    label: '建造时间',
    key: 'constructionTime',
    required: false,
    description: '建造时间，可选，格式：YYYY-MM-DD',
    width: 15,
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '历史文献记载，可选',
    width: 50,
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
  },
];

/**
 * 山塬字段定义（通用）
 */
const MOUNTAIN_FIELDS = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '山塬名称，必填，最大255字符',
    width: 20,
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '山塬编号，必填，最大50字符，系统内唯一',
    width: 15,
  },
  {
    label: '类型名称',
    key: 'typeName',
    required: false,
    description: '所属类型名称，可选，固定为“山塬”，需要在类型字典中存在',
    width: 15,
  },
  {
    label: '经度',
    key: 'longitude',
    required: false,
    description: '山塬经度坐标，可选，范围-180到180',
    width: 15,
  },
  {
    label: '纬度',
    key: 'latitude',
    required: false,
    description: '山塬纬度坐标，可选，范围-90到90',
    width: 15,
  },
  {
    label: '高度',
    key: 'height',
    required: false,
    description: '山塬高度，可选，单位：米',
    width: 10,
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '相关历史文献记载，可选',
    width: 50,
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
  },
];

/**
 * 水系字段定义（通用）
 */
const WATER_SYSTEM_FIELDS = [
  {
    label: '名称',
    key: 'name',
    required: true,
    description: '水系名称，必填，最大255字符',
    width: 20,
  },
  {
    label: '编号',
    key: 'code',
    required: true,
    description: '水系编号，必填，最大50字符，系统内唯一',
    width: 15,
  },
  {
    label: '类型名称',
    key: 'typeName',
    required: false,
    description: '所属类型名称，可选，固定为“水系”，需要在类型字典中存在',
    width: 15,
  },
  {
    label: '经度',
    key: 'longitude',
    required: false,
    description: '水系经度坐标，可选，范围-180到180',
    width: 15,
  },
  {
    label: '纬度',
    key: 'latitude',
    required: false,
    description: '水系纬度坐标，可选，范围-90到90',
    width: 15,
  },
  {
    label: '长度/面积',
    key: 'lengthArea',
    required: false,
    description: '水系长度或面积，可选，最大50字符',
    width: 15,
  },
  {
    label: '历史记载',
    key: 'historicalRecords',
    required: false,
    description: '相关历史文献记载，可选',
    width: 50,
  },
  {
    label: '区域名称',
    key: 'regionName',
    required: true,
    description: '所属区域名称，必填，需要在区域字典中存在',
    width: 15,
  },
];

/**
 * 要素关联字段定义（通用）
 */
const RELATIONSHIP_FIELDS = [
  {
    label: '关系类型',
    key: 'relationName',
    required: false,
    description: '关系类型名称，可选，需要在关系字典中存在',
    width: 15,
  },
  {
    label: '源要素类型',
    key: 'sourceType',
    required: true,
    description: '源要素类型，必填，可选值：山塬、水系、历史要素',
    width: 15,
  },
  {
    label: '源要素名称',
    key: 'sourceName',
    required: true,
    description: '源要素名称，必填，需要在对应类型的数据中存在',
    width: 20,
  },
  {
    label: '目标类型',
    key: 'targetType',
    required: true,
    description: '目标类型，必填，可选值：要素、类别',
    width: 15,
  },
  {
    label: '目标要素类型',
    key: 'targetEntityType',
    required: false,
    description:
      '目标要素类型，当目标类型为要素时必填，可选值：山塬、水系、历史要素、类型字典、区域字典',
    width: 15,
  },
  {
    label: '目标要素名称',
    key: 'targetName',
    required: true,
    description: '目标要素名称，必填，需要在对应类型的数据中存在',
    width: 20,
  },
  {
    label: '关联方向',
    key: 'direction',
    required: false,
    description: '关联方向，可选，如：前有、后有、东连、西连等',
    width: 15,
  },
  {
    label: '词条描述',
    key: 'term',
    required: false,
    description: '词条描述，可选，最大255字符',
    width: 25,
  },
  {
    label: '记载内容',
    key: 'record',
    required: false,
    description: '详细的记载内容，可选',
    width: 40,
  },
  {
    label: '排序号',
    key: 'sort',
    required: false,
    description: '显示排序号，可选，数字类型',
    width: 10,
  },
];

/**
 * 历史要素Excel配置
 */
export const HISTORICAL_ELEMENT_EXCEL_CONFIG = {
  /**
   * 导入模板配置
   */
  IMPORT: {
    title: '历史要素批量导入模板',
    instructions: [
      '请按照要求填写历史要素数据',
      '带*号的字段为必填项',
      '类型名称和区域名称请填写准确的名称，系统会自动转换为对应ID',
      '填写完成后保存并上传',
    ],
    headers: HISTORICAL_ELEMENT_FIELDS,
    exampleData: [], // 暂时移除示例数据以避免内存问题
    sheetName: '历史要素数据',
    styleConfig: {
      enabled: true,
      theme: 'blue',
      showBorders: true,
      highlightRequired: true,
    },
  } as ExcelTemplateConfig,

  /**
   * 导出配置
   */
  EXPORT: {
    title: '历史要素数据导出',
    headers: HISTORICAL_ELEMENT_FIELDS.map(field => ({
      ...field,
      description: undefined, // 导出时不需要字段说明
    })),
    sheetName: '历史要素数据',
  } as ExcelTemplateConfig,

  /**
   * 报表配置
   */
  REPORT: {
    title: '历史要素统计报表',
    headers: [
      { label: '区域名称', key: 'regionName', width: 20 },
      { label: '要素数量', key: 'elementCount', width: 15 },
      { label: '类型分布', key: 'typeDistribution', width: 30 },
      { label: '统计时间', key: 'statisticsTime', width: 20 },
    ],
    sheetName: '统计报表',
  } as ExcelTemplateConfig,
};

/**
 * 山塬Excel配置
 */
export const MOUNTAIN_EXCEL_CONFIG = {
  /**
   * 导入模板配置
   */
  IMPORT: {
    title: '山塬批量导入模板',
    instructions: [
      '请按照要求填写山塬数据',
      '带*号的字段为必填项',
      '区域名称请填写准确的名称，系统会自动转换为对应ID',
      '填写完成后保存并上传',
    ],
    headers: MOUNTAIN_FIELDS,
    exampleData: [],
    sheetName: '山塬数据',
    styleConfig: {
      enabled: true,
      theme: 'green',
      showBorders: true,
      highlightRequired: true,
    },
  } as ExcelTemplateConfig,

  /**
   * 导出配置
   */
  EXPORT: {
    title: '山塬数据导出',
    headers: MOUNTAIN_FIELDS.map(field => ({
      ...field,
      description: undefined,
    })),
    sheetName: '山塬数据',
  } as ExcelTemplateConfig,

  /**
   * 报表配置
   */
  REPORT: {
    title: '山塬统计报表',
    headers: [
      { label: '区域名称', key: 'regionName', width: 20 },
      { label: '山塬数量', key: 'mountainCount', width: 15 },
      { label: '平均高度', key: 'avgHeight', width: 15 },
      { label: '最高海拔', key: 'maxHeight', width: 15 },
      { label: '统计时间', key: 'statisticsTime', width: 20 },
    ],
    sheetName: '山塬统计报表',
  } as ExcelTemplateConfig,
};

/**
 * 水系Excel配置
 */
export const WATER_SYSTEM_EXCEL_CONFIG = {
  /**
   * 导入模板配置
   */
  IMPORT: {
    title: '水系批量导入模板',
    instructions: [
      '请按照要求填写水系数据',
      '带*号的字段为必填项',
      '区域名称请填写准确的名称，系统会自动转换为对应ID',
      '填写完成后保存并上传',
    ],
    headers: WATER_SYSTEM_FIELDS,
    exampleData: [],
    sheetName: '水系数据',
    styleConfig: {
      enabled: true,
      theme: 'blue',
      showBorders: true,
      highlightRequired: true,
    },
  } as ExcelTemplateConfig,

  /**
   * 导出配置
   */
  EXPORT: {
    title: '水系数据导出',
    headers: WATER_SYSTEM_FIELDS.map(field => ({
      ...field,
      description: undefined,
    })),
    sheetName: '水系数据',
  } as ExcelTemplateConfig,

  /**
   * 报表配置
   */
  REPORT: {
    title: '水系统计报表',
    headers: [
      { label: '区域名称', key: 'regionName', width: 20 },
      { label: '水系数量', key: 'waterSystemCount', width: 15 },
      { label: '总长度/面积', key: 'totalLengthArea', width: 20 },
      { label: '统计时间', key: 'statisticsTime', width: 20 },
    ],
    sheetName: '水系统计报表',
  } as ExcelTemplateConfig,
};

/**
 * 要素关联Excel配置
 */
export const RELATIONSHIP_EXCEL_CONFIG = {
  /**
   * 导入模板配置
   */
  IMPORT: {
    title: '要素关联批量导入模板',
    instructions: [
      '请按照要求填写要素关联数据',
      '带*号的字段为必填项',
      '源要素类型和目标要素类型请填写准确的名称',
      '要素名称需要在对应类型的数据中存在',
      '填写完成后保存并上传',
    ],
    headers: RELATIONSHIP_FIELDS,
    exampleData: [],
    sheetName: '要素关联数据',
    styleConfig: {
      enabled: true,
      theme: 'orange',
      showBorders: true,
      highlightRequired: true,
    },
  } as ExcelTemplateConfig,

  /**
   * 导出配置
   */
  EXPORT: {
    title: '要素关联数据导出',
    headers: RELATIONSHIP_FIELDS.map(field => ({
      ...field,
      description: undefined,
    })),
    sheetName: '要素关联数据',
  } as ExcelTemplateConfig,

  /**
   * 报表配置
   */
  REPORT: {
    title: '要素关联统计报表',
    headers: [
      { label: '源要素类型', key: 'sourceType', width: 15 },
      { label: '关联数量', key: 'relationshipCount', width: 15 },
      { label: '目标类型分布', key: 'targetTypeDistribution', width: 30 },
      { label: '统计时间', key: 'statisticsTime', width: 20 },
    ],
    sheetName: '要素关联统计报表',
  } as ExcelTemplateConfig,
};

/**
 * 用户Excel配置（示例）
 */
export const USER_EXCEL_CONFIG = {
  IMPORT: {
    title: '用户信息批量导入模板',
    instructions: [
      '请按照以下要求填写用户数据：',
      '1. 带*号的字段为必填项',
      '2. 邮箱格式必须正确',
      '3. 手机号格式必须正确',
    ],
    headers: [
      {
        label: '用户名',
        key: 'username',
        required: true,
        description: '用户登录名，必填，最大50字符',
        width: 20,
      },
      {
        label: '邮箱',
        key: 'email',
        required: true,
        description: '用户邮箱，必填，格式：<EMAIL>',
        width: 30,
      },
      {
        label: '手机号',
        key: 'phone',
        required: false,
        description: '手机号码，可选，格式：13800138000',
        width: 15,
      },
      {
        label: '真实姓名',
        key: 'realName',
        required: false,
        description: '用户真实姓名，可选',
        width: 20,
      },
    ],
    exampleData: [
      {
        username: 'zhangsan',
        email: '<EMAIL>',
        phone: '13800138000',
        realName: '张三',
      },
    ],
    sheetName: '用户数据',
  } as ExcelTemplateConfig,

  EXPORT: {
    title: '用户信息导出',
    headers: [
      { label: '用户名', key: 'username', width: 20 },
      { label: '邮箱', key: 'email', width: 30 },
      { label: '真实姓名', key: 'realName', width: 20 },
      { label: '创建时间', key: 'createdAt', width: 20 },
    ],
    sheetName: '用户数据',
  } as ExcelTemplateConfig,
};

/**
 * 配置类型定义
 */
export type ExcelConfigType = 'IMPORT' | 'EXPORT' | 'REPORT';

/**
 * 获取Excel配置的工具函数
 */
export function getExcelConfig(
  module: string,
  type: ExcelConfigType
): ExcelTemplateConfig {
  const configs: Record<string, any> = {
    HISTORICAL_ELEMENT: HISTORICAL_ELEMENT_EXCEL_CONFIG,
    MOUNTAIN: MOUNTAIN_EXCEL_CONFIG,
    WATER_SYSTEM: WATER_SYSTEM_EXCEL_CONFIG,
    RELATIONSHIP: RELATIONSHIP_EXCEL_CONFIG,
    USER: USER_EXCEL_CONFIG,
  };

  const moduleConfig = configs[module];
  if (!moduleConfig) {
    throw new Error(`未找到模块 ${module} 的Excel配置`);
  }

  const typeConfig = moduleConfig[type];
  if (!typeConfig) {
    throw new Error(`未找到模块 ${module} 的 ${type} 配置`);
  }

  return typeConfig;
}

/**
 * 便捷访问方法
 */
export const ExcelConfigs = {
  HistoricalElement: {
    Import: HISTORICAL_ELEMENT_EXCEL_CONFIG.IMPORT,
    Export: HISTORICAL_ELEMENT_EXCEL_CONFIG.EXPORT,
    Report: HISTORICAL_ELEMENT_EXCEL_CONFIG.REPORT,
  },
  Mountain: {
    Import: MOUNTAIN_EXCEL_CONFIG.IMPORT,
    Export: MOUNTAIN_EXCEL_CONFIG.EXPORT,
    Report: MOUNTAIN_EXCEL_CONFIG.REPORT,
  },
  WaterSystem: {
    Import: WATER_SYSTEM_EXCEL_CONFIG.IMPORT,
    Export: WATER_SYSTEM_EXCEL_CONFIG.EXPORT,
    Report: WATER_SYSTEM_EXCEL_CONFIG.REPORT,
  },
  Relationship: {
    Import: RELATIONSHIP_EXCEL_CONFIG.IMPORT,
    Export: RELATIONSHIP_EXCEL_CONFIG.EXPORT,
    Report: RELATIONSHIP_EXCEL_CONFIG.REPORT,
  },
  User: {
    Import: USER_EXCEL_CONFIG.IMPORT,
    Export: USER_EXCEL_CONFIG.EXPORT,
  },
};
