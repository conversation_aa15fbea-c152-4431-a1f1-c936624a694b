import { Provide } from '@midwayjs/core';
import { Op } from 'sequelize';
import { Relationship } from '../entity/relationship.entity';
import { Mountain } from '../entity/mountain.entity';
import { WaterSystem } from '../entity/water-system.entity';
import { HistoricalElement } from '../entity/historical-element.entity';
import { RelationshipDict } from '../entity/relationship-dict.entity';
import { TypeDict } from '../entity/type-dict.entity';
import { RegionDict } from '../entity/region-dict.entity';
import { PageResponseDTO } from '../dto/common.dto';
import {
  CreateRelationshipDTO,
  UpdateRelationshipDTO,
  RelationshipQueryDTO,
  RelationshipResponseDTO,
  NetworkGraphDTO,
  NetworkNodeDTO,
  NetworkLinkDTO,
  RelationshipStatisticsDTO,
} from '../dto/relationship.dto';

@Provide()
export class RelationshipService {
  /**
   * 创建关系
   */
  async createRelationship(
    createDto: CreateRelationshipDTO
  ): Promise<RelationshipResponseDTO> {
    // 验证源要素是否存在
    await this.validateSourceElement(createDto.sourceType, createDto.sourceId);

    // 验证目标要素是否存在
    await this.validateTargetElement(
      createDto.targetType,
      createDto.targetEntityType,
      createDto.targetId
    );

    // 验证关系类型是否存在（如果提供了）
    if (createDto.relationDictId) {
      await this.validateRelationDict(createDto.relationDictId);
    }

    const relationship = await Relationship.create(createDto as any);

    return this.getRelationshipById(relationship.id);
  }

  /**
   * 更新关系
   */
  async updateRelationship(
    id: number,
    updateDto: UpdateRelationshipDTO
  ): Promise<RelationshipResponseDTO> {
    const relationship = await Relationship.findByPk(id);
    if (!relationship) {
      throw new Error('关系不存在');
    }

    // 验证更新的数据
    if (updateDto.sourceType && updateDto.sourceId) {
      await this.validateSourceElement(
        updateDto.sourceType,
        updateDto.sourceId
      );
    }

    if (updateDto.targetType && updateDto.targetId) {
      await this.validateTargetElement(
        updateDto.targetType,
        updateDto.targetEntityType,
        updateDto.targetId
      );
    }

    if (updateDto.relationDictId) {
      await this.validateRelationDict(updateDto.relationDictId);
    }

    await relationship.update(updateDto);

    return this.getRelationshipById(id);
  }

  /**
   * 删除关系
   */
  async deleteRelationship(id: number): Promise<void> {
    const relationship = await Relationship.findByPk(id);
    if (!relationship) {
      throw new Error('关系不存在');
    }

    await relationship.destroy();
  }

  /**
   * 根据ID获取关系
   */
  async getRelationshipById(id: number): Promise<RelationshipResponseDTO> {
    const relationship = await Relationship.findByPk(id, {
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
        {
          model: Relationship,
          as: 'parent',
          attributes: ['id'],
        },
        {
          model: Relationship,
          as: 'children',
          attributes: ['id', 'status'],
          where: { status: 1 },
          required: false,
        },
      ],
    });

    if (!relationship) {
      throw new Error('关系不存在');
    }

    const result = new RelationshipResponseDTO(relationship.toJSON());

    // 获取源要素信息
    result.sourceElement = await this.getElementInfo(
      relationship.sourceType,
      relationship.sourceId
    );

    // 获取目标要素信息
    result.targetElement = await this.getTargetElementInfo(
      relationship.targetType,
      relationship.targetEntityType,
      relationship.targetId
    );

    return result;
  }

  /**
   * 分页查询关系
   */
  async getRelationshipPage(
    queryDto: RelationshipQueryDTO
  ): Promise<PageResponseDTO<RelationshipResponseDTO>> {
    const { page = 1, pageSize = 10, keyword, ...filters } = queryDto;
    const offset = (page - 1) * pageSize;

    const whereCondition: any = {};

    if (keyword) {
      whereCondition[Op.or] = [
        { term: { [Op.like]: `%${keyword}%` } },
        { record: { [Op.like]: `%${keyword}%` } },
      ];
    }

    // 添加其他筛选条件
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined) {
        whereCondition[key] = filters[key];
      }
    });

    const { count, rows } = await Relationship.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
        {
          model: Relationship,
          as: 'parent',
          attributes: ['id'],
        },
      ],
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
      limit: pageSize,
      offset,
    });

    const data = await Promise.all(
      rows.map(async row => {
        const result = new RelationshipResponseDTO(row.toJSON());

        // 获取源要素信息
        result.sourceElement = await this.getElementInfo(
          row.sourceType,
          row.sourceId
        );

        // 获取目标要素信息
        result.targetElement = await this.getTargetElementInfo(
          row.targetType,
          row.targetEntityType,
          row.targetId
        );

        return result;
      })
    );

    return new PageResponseDTO(data, count, page, pageSize);
  }

  /**
   * 获取关系列表（不分页）
   */
  async getRelationshipList(filters?: any): Promise<RelationshipResponseDTO[]> {
    const whereCondition = filters || {};

    const relationships = await Relationship.findAll({
      where: whereCondition,
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
        {
          model: Relationship,
          as: 'parent',
          attributes: ['id'],
        },
      ],
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
    });

    return Promise.all(
      relationships.map(async relationship => {
        const result = new RelationshipResponseDTO(relationship.toJSON());

        // 获取源要素信息
        result.sourceElement = await this.getElementInfo(
          relationship.sourceType,
          relationship.sourceId
        );

        // 获取目标要素信息
        result.targetElement = await this.getTargetElementInfo(
          relationship.targetType,
          relationship.targetEntityType,
          relationship.targetId
        );

        return result;
      })
    );
  }

  /**
   * 批量创建关系
   */
  async batchCreateRelationships(
    relationships: CreateRelationshipDTO[]
  ): Promise<void> {
    // 验证所有数据
    for (const relationship of relationships) {
      await this.validateSourceElement(
        relationship.sourceType,
        relationship.sourceId
      );
      await this.validateTargetElement(
        relationship.targetType,
        relationship.targetEntityType,
        relationship.targetId
      );
      if (relationship.relationDictId) {
        await this.validateRelationDict(relationship.relationDictId);
      }
    }

    await Relationship.bulkCreate(relationships as any);
  }

  /**
   * 批量导入关系（Excel导入专用）
   */
  async batchImportRelationships(
    relationships: CreateRelationshipDTO[]
  ): Promise<{
    successCount: number;
    failureCount: number;
    errors: Array<{ index: number; error: string; data: any }>;
  }> {
    const result = {
      successCount: 0,
      failureCount: 0,
      errors: [] as Array<{ index: number; error: string; data: any }>,
    };

    const validRelationships: CreateRelationshipDTO[] = [];

    // 逐个验证数据
    for (let i = 0; i < relationships.length; i++) {
      const relationship = relationships[i];
      try {
        // 验证源要素
        await this.validateSourceElement(
          relationship.sourceType,
          relationship.sourceId
        );

        // 验证目标要素
        await this.validateTargetElement(
          relationship.targetType,
          relationship.targetEntityType,
          relationship.targetId
        );

        // 验证关系类型（如果提供了）
        if (relationship.relationDictId) {
          await this.validateRelationDict(relationship.relationDictId);
        }

        // 检查是否已存在相同的关系
        const existingRelation = await this.checkDuplicateRelationship(
          relationship
        );
        if (existingRelation) {
          result.failureCount++;
          result.errors.push({
            index: i + 1,
            error: '该关系已存在',
            data: relationship,
          });
          continue;
        }

        validRelationships.push(relationship);
      } catch (error) {
        result.failureCount++;
        result.errors.push({
          index: i + 1,
          error: error.message,
          data: relationship,
        });
      }
    }

    // 批量创建有效的关系
    if (validRelationships.length > 0) {
      try {
        await Relationship.bulkCreate(validRelationships as any);
        result.successCount = validRelationships.length;
      } catch (error) {
        // 如果批量创建失败，尝试逐个创建
        for (let i = 0; i < validRelationships.length; i++) {
          try {
            await Relationship.create(validRelationships[i] as any);
            result.successCount++;
          } catch (createError) {
            result.failureCount++;
            result.errors.push({
              index: i + 1,
              error: createError.message,
              data: validRelationships[i],
            });
          }
        }
      }
    }

    return result;
  }

  /**
   * 检查是否存在重复的关系
   */
  private async checkDuplicateRelationship(
    relationship: CreateRelationshipDTO
  ): Promise<boolean> {
    const existing = await Relationship.findOne({
      where: {
        sourceType: relationship.sourceType,
        sourceId: relationship.sourceId,
        targetType: relationship.targetType,
        targetEntityType: relationship.targetEntityType,
        targetId: relationship.targetId,
        status: 1,
      },
    });
    return !!existing;
  }

  /**
   * 批量更新状态
   */
  async batchUpdateStatus(ids: number[], status: number): Promise<void> {
    await Relationship.update(
      { status },
      {
        where: {
          id: {
            [Op.in]: ids,
          },
        },
      }
    );
  }

  /**
   * 获取网络图数据
   */
  async getNetworkGraphData(filters?: any): Promise<NetworkGraphDTO> {
    const relationships = await this.getRelationshipList(filters);

    const nodes = new Map<string, NetworkNodeDTO>();
    const links: NetworkLinkDTO[] = [];
    const categories = new Set<string>();

    // 处理关联关系，生成节点和连线
    for (const relationship of relationships) {
      // 添加源节点
      const sourceKey = `${relationship.sourceType}_${relationship.sourceId}`;
      if (!nodes.has(sourceKey)) {
        const sourceNode: NetworkNodeDTO = {
          id: sourceKey,
          name:
            relationship.sourceElement?.name ||
            `${relationship.sourceType}_${relationship.sourceId}`,
          type: relationship.sourceType,
          category: this.getNodeCategory(relationship.sourceType),
          size: 10,
          color: this.getNodeColor(relationship.sourceType),
        };
        nodes.set(sourceKey, sourceNode);
        categories.add(sourceNode.category);
      }

      // 添加目标节点
      const targetKey = `${
        relationship.targetEntityType || relationship.targetType
      }_${relationship.targetId}`;
      if (!nodes.has(targetKey)) {
        const targetNode: NetworkNodeDTO = {
          id: targetKey,
          name:
            relationship.targetElement?.name ||
            `${relationship.targetEntityType || relationship.targetType}_${
              relationship.targetId
            }`,
          type: relationship.targetEntityType || relationship.targetType,
          category: this.getNodeCategory(
            relationship.targetEntityType || relationship.targetType
          ),
          size: 10,
          color: this.getNodeColor(
            relationship.targetEntityType || relationship.targetType
          ),
        };
        nodes.set(targetKey, targetNode);
        categories.add(targetNode.category);
      }

      // 添加连线
      const link: NetworkLinkDTO = {
        source: sourceKey,
        target: targetKey,
        relation: relationship.relationDict?.relationName || '关联',
        direction: relationship.direction,
        term: relationship.term,
        weight: 1,
        color: this.getLinkColor(relationship.relationDict?.relationCode),
      };
      links.push(link);
    }

    return {
      nodes: Array.from(nodes.values()),
      links,
      categories: Array.from(categories),
    };
  }

  /**
   * 获取关系统计
   */
  async getRelationshipStatistics(
    filters?: any
  ): Promise<RelationshipStatisticsDTO> {
    const whereCondition = filters || {};

    // 总数统计
    const total = await Relationship.count({ where: whereCondition });

    // 按源要素类型统计
    const bySourceType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'sourceType',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['sourceType'],
      raw: true,
    });

    // 按目标类型统计
    const byTargetType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'targetType',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['targetType'],
      raw: true,
    });

    // 按关系类型统计
    const byRelationType = await Relationship.findAll({
      where: whereCondition,
      attributes: [
        'relationDictId',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['relationName'],
        },
      ],
      group: ['relationDictId', 'relationDict.id'],
      raw: false,
    });

    // 按方向统计
    const byDirection = await Relationship.findAll({
      where: {
        ...whereCondition,
        direction: { [Op.ne]: null },
      },
      attributes: [
        'direction',
        [Relationship.sequelize.fn('COUNT', '*'), 'count'],
      ],
      group: ['direction'],
      raw: true,
    });

    return {
      total,
      bySourceType: bySourceType.map((item: any) => ({
        sourceType: item.sourceType,
        count: parseInt(item.count),
      })),
      byTargetType: byTargetType.map((item: any) => ({
        targetType: item.targetType,
        count: parseInt(item.count),
      })),
      byRelationType: byRelationType.map((item: any) => ({
        relationId: item.relationDictId,
        relationName: item.relationDict?.relationName || '未知',
        count: parseInt(item.get('count')),
      })),
      byDirection: byDirection.map((item: any) => ({
        direction: item.direction,
        count: parseInt(item.count),
      })),
    };
  }

  /**
   * 根据要素获取关联关系
   */
  async getRelationsByElement(
    elementType: string,
    elementId: number
  ): Promise<RelationshipResponseDTO[]> {
    const relationships = await Relationship.findAll({
      where: {
        [Op.or]: [
          { sourceType: elementType, sourceId: elementId },
          { targetEntityType: elementType, targetId: elementId },
        ],
        status: 1,
      },
      include: [
        {
          model: RelationshipDict,
          as: 'relationDict',
          attributes: ['id', 'relationName', 'relationCode'],
        },
        {
          model: Relationship,
          as: 'parent',
          attributes: ['id'],
        },
      ],
      order: [
        ['sort', 'ASC'],
        ['createdAt', 'DESC'],
      ],
    });

    return Promise.all(
      relationships.map(async relationship => {
        const result = new RelationshipResponseDTO(relationship.toJSON());

        // 获取源要素信息
        result.sourceElement = await this.getElementInfo(
          relationship.sourceType,
          relationship.sourceId
        );

        // 获取目标要素信息
        result.targetElement = await this.getTargetElementInfo(
          relationship.targetType,
          relationship.targetEntityType,
          relationship.targetId
        );

        return result;
      })
    );
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 验证源要素是否存在
   */
  private async validateSourceElement(
    sourceType: string,
    sourceId: number
  ): Promise<void> {
    const element = await this.getElementInfo(sourceType, sourceId);
    if (!element) {
      throw new Error(`源要素不存在: ${sourceType}#${sourceId}`);
    }
  }

  /**
   * 验证目标要素是否存在
   */
  private async validateTargetElement(
    targetType: string,
    targetEntityType: string,
    targetId: number
  ): Promise<void> {
    const element = await this.getTargetElementInfo(
      targetType,
      targetEntityType,
      targetId
    );
    if (!element) {
      throw new Error(`目标要素不存在: ${targetType}#${targetId}`);
    }
  }

  /**
   * 验证关系类型是否存在
   */
  private async validateRelationDict(relationDictId: number): Promise<void> {
    const relationDict = await RelationshipDict.findByPk(relationDictId);
    if (!relationDict) {
      throw new Error(`关系类型不存在: ${relationDictId}`);
    }
  }

  /**
   * 获取目标要素信息（根据 targetType 区分处理逻辑）
   */
  private async getTargetElementInfo(
    targetType: string,
    targetEntityType: string,
    targetId: number
  ): Promise<any> {
    if (targetType === 'category') {
      // 如果目标类型是类别，查询 type_dict 表，并获取关联的历史要素
      const typeDict = await TypeDict.findByPk(targetId, {
        attributes: ['id', 'typeName', 'typeCode'],
      });

      if (!typeDict) {
        return null;
      }

      // 查询该类别下的历史要素
      const historicalElements = await HistoricalElement.findAll({
        where: { typeDictId: targetId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']],
      });

      return {
        ...typeDict.toJSON(),
        name: typeDict.typeName, // 统一使用 name 字段
        relatedElements: historicalElements.map(el => el.toJSON()),
      };
    } else {
      // 如果目标类型是要素，按 targetEntityType 查询相应的表
      const entityType = targetEntityType || targetType;
      return await this.getElementInfo(entityType, targetId);
    }
  }

  /**
   * 获取要素信息
   */
  private async getElementInfo(
    elementType: string,
    elementId: number
  ): Promise<any> {
    let model: any;
    switch (elementType) {
      case 'mountain':
        model = Mountain;
        break;
      case 'water_system':
        model = WaterSystem;
        break;
      case 'historical_element':
        model = HistoricalElement;
        break;
      case 'type_dict':
        model = TypeDict;
        break;
      case 'region_dict':
        model = RegionDict;
        break;
      default:
        return null;
    }

    return await model.findByPk(elementId, {
      attributes: ['id', 'name', 'code'],
    });
  }

  /**
   * 获取节点分类
   */
  private getNodeCategory(elementType: string): string {
    const categoryMap = {
      mountain: '山塬',
      water_system: '水系',
      historical_element: '历史要素',
      type_dict: '类型',
      region_dict: '区域',
    };
    return categoryMap[elementType] || '其他';
  }

  /**
   * 获取节点颜色
   */
  private getNodeColor(elementType: string): string {
    const colorMap = {
      mountain: '#8B4513', // 棕色 - 山塬
      water_system: '#4169E1', // 蓝色 - 水系
      historical_element: '#DC143C', // 红色 - 历史要素
      type_dict: '#32CD32', // 绿色 - 类型
      region_dict: '#FFD700', // 金色 - 区域
    };
    return colorMap[elementType] || '#808080';
  }

  /**
   * 获取连线颜色
   */
  private getLinkColor(relationCode?: string): string {
    const colorMap = {
      spatial: '#4169E1', // 空间关系 - 蓝色
      visual: '#9932CC', // 视线关系 - 紫色
      historical: '#DC143C', // 历史关系 - 红色
      functional: '#32CD32', // 功能关系 - 绿色
    };

    if (!relationCode) return '#808080';

    // 根据关系编码前缀判断类型
    if (relationCode.startsWith('SP')) return colorMap.spatial;
    if (relationCode.startsWith('VS')) return colorMap.visual;
    if (relationCode.startsWith('HT')) return colorMap.historical;
    if (relationCode.startsWith('FN')) return colorMap.functional;

    return '#808080';
  }
}
